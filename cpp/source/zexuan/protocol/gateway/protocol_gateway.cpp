#include "zexuan/protocol/gateway/protocol_gateway.hpp"

#include <spdlog/spdlog.h>

#include <fstream>

#include "zexuan/protocol/service/protocol_service.hpp"
#include "zexuan/protocol/transform/protocol_transform.hpp"
#include "zexuan/utils/invoke_id_utils.hpp"

namespace zexuan {
  namespace protocol {
    namespace gateway {

      ProtocolGateway::ProtocolGateway(const std::string& config_file_path,
                                       std::shared_ptr<base::Mediator> mediator)
          : mediator_(mediator), config_file_path_(config_file_path) {
        // 读取配置文件
        std::ifstream config_file(config_file_path_);
        if (!config_file.is_open()) {
          spdlog::warn("Cannot open config file: {}, using default values", config_file_path_);
          // 使用默认值
          request_timeout_seconds_ = 30;
          thread_sleep_ms_ = 10;
          max_pending_requests_ = 1000;
        } else {
          nlohmann::json config;
          config_file >> config;

          // 读取网关配置
          auto gateway_config = config["protocol"]["gateway"];
          request_timeout_seconds_ = gateway_config.value("request_timeout_seconds", 30);
          thread_sleep_ms_ = gateway_config.value("thread_sleep_ms", 10);
          max_pending_requests_ = gateway_config.value("max_pending_requests", 1000);
          protocol_type_ = gateway_config.value("protocol_type", "gw104");
        }

        // 创建Observer - 使用固定ID接收来自Service层的响应和事件
        observer_ = std::make_shared<base::Observer>(base::GATEWAY_OBSERVER_ID, mediator_);

        // 设置Observer的结果回调 - 处理来自Service层的响应
        observer_->SetResultCallback(
            [this](const base::CommonMessage& message) -> base::Result<void> {
              AddServiceResponseToRequest(message);
              return base::Result<void>{};
            });

        // 设置Observer的事件回调 - 处理来自Service层的事件
        observer_->SetEventCallback(
            [this](const base::EventMessage& message) -> base::Result<void> {
              OnServiceEvent(message);
              return base::Result<void>{};
            });

        // 设置Observer关注的事件类型（关注统一的事件类型）
        base::EventTypeList event_types = {1};  // 关注统一的事件类型1
        observer_->SetCareEventType(event_types);

        // 创建Subject - 使用固定ID向Service层发送命令
        subject_ = std::make_shared<base::Subject>(base::GATEWAY_SUBJECT_ID, mediator_);

        // 创建协议转换器
        if (!CreateProtocolTransform()) {
          spdlog::error("Failed to create protocol transform");
          throw std::runtime_error("Failed to create protocol transform");
        }

        // 创建协议服务
        if (!CreateProtocolService()) {
          spdlog::error("Failed to create protocol service");
          throw std::runtime_error("Failed to create protocol service");
        }

        spdlog::info("ProtocolGateway created with Observer ID: {}, Subject ID: {}, protocol: {}",
                     base::GATEWAY_OBSERVER_ID, base::GATEWAY_SUBJECT_ID, protocol_type_);
      }

      ProtocolGateway::~ProtocolGateway() {
        Stop();
        spdlog::info("ProtocolGateway destroyed");
      }

      bool ProtocolGateway::Start() {
        if (is_running_.load()) {
          spdlog::warn("Gateway already running");
          return true;
        }

        try {
          // 初始化Observer（包含注册到Mediator）
          auto observer_result = observer_->Init();
          if (!observer_result) {
            spdlog::error("Failed to initialize Observer: {}",
                          static_cast<int>(observer_result.error()));
            return false;
          }

          // 初始化Subject（包含注册到Mediator）
          auto subject_result = subject_->Init();
          if (!subject_result) {
            spdlog::error("Failed to initialize Subject: {}",
                          static_cast<int>(subject_result.error()));
            return false;
          }

          // 启动4个核心线程
          should_stop_.store(false);
          protocol_frame_processor_thread_
              = std::thread(&ProtocolGateway::ProtocolFrameProcessorLoop, this);
          local_task_processor_thread_
              = std::thread(&ProtocolGateway::LocalTaskProcessorLoop, this);
          response_matcher_thread_ = std::thread(&ProtocolGateway::ResponseMatcherLoop, this);
          event_processor_thread_ = std::thread(&ProtocolGateway::EventProcessorLoop, this);

          is_running_.store(true);
          spdlog::info("Gateway started successfully");
          return true;

        } catch (const std::exception& e) {
          spdlog::error("Failed to start Gateway: {}", e.what());
          return false;
        }
      }

      void ProtocolGateway::Stop() {
        if (!is_running_.load()) {
          return;
        }

        spdlog::info("Stopping Gateway");

        // 停止线程
        should_stop_.store(true);

        // 通知所有条件变量
        protocol_cmd_queue_cv_.notify_all();
        local_task_queue_cv_.notify_all();
        event_queue_cv_.notify_all();

        // 等待4个核心线程结束
        if (protocol_frame_processor_thread_.joinable()) protocol_frame_processor_thread_.join();
        if (local_task_processor_thread_.joinable()) local_task_processor_thread_.join();
        if (response_matcher_thread_.joinable()) response_matcher_thread_.join();
        if (event_processor_thread_.joinable()) event_processor_thread_.join();

        // 使用Exit()方法注销Observer和Subject
        if (observer_) observer_->Exit();
        if (subject_) subject_->Exit();

        is_running_.store(false);
        spdlog::info("Gateway stopped");
      }

      void ProtocolGateway::OnNetworkProtocolData(const std::vector<uint8_t>& protocol_data) {
        spdlog::debug("Gateway received protocol data, length: {}", protocol_data.size());

        // 解析协议帧
        base::ProtocolFrame frame;
        if (!ParseProtocolFrame(protocol_data, frame)) {
          spdlog::error("Failed to parse protocol frame");
          return;
        }

        // 每个Gateway对应一个连接，不需要设置frame_id
        // frame.frame_id 由协议帧本身决定

        // 简单地将协议帧放入原始队列，让 ProtocolFrameProcessorLoop 处理（参考原始 m_ProCmdDeque）
        {
          std::lock_guard<std::mutex> lock(protocol_cmd_queue_mutex_);
          protocol_cmd_queue_.push(frame);
        }
        protocol_cmd_queue_cv_.notify_one();
      }

      // 新增的协议帧处理方法实现

      void ProtocolGateway::HandleSingleFrameCommand(const base::ProtocolFrame& frame) {
        // 单帧召唤命令处理（参考原始 CVT_TO_CALL 处理）
        spdlog::debug("Handling single frame command from connection {}", frame.frame_id);

        base::ProtocolFrameList frame_list = {frame};
        std::vector<base::CommonMessage> common_list;
        base::ProtocolFrameList result_frames;

        int ret
            = protocol_transform_->ConvertProToCommonMsg(frame_list, common_list, result_frames);
        if (ret != 0) {
          spdlog::error("Failed to convert single frame to common message");
          return;
        }

        // 处理转换失败生成的回应结果
        if (!result_frames.empty()) {
          for (const auto& result_frame : result_frames) {
            if (send_callback_) {
              send_callback_(result_frame.data);
            }
          }
          return;  // 转换失败，直接返回，不需要等待响应
        }

        // 处理转换成功的召唤命令（参考原始 __AddProCallCmdToAskResDeque 逻辑）
        if (!common_list.empty()) {
          // 1. 先创建响应匹配包
          if (!AddFrameToResponseRequest(frame, false)) {  // false = 召唤命令
            spdlog::error("Failed to add call command to response request");
            return;
          }

          // 2. 发送所有召唤命令到Service
          std::string request_key = GenerateResponseRequestKey(frame);

          for (auto& common_msg : common_list) {
            common_msg.source_id = frame.frame_id;
            common_msg.target_id = base::SERVICE_SUBJECT_ID;
            common_msg.invoke_id = utils::invoke_id::Generate(base::GATEWAY_OBSERVER_ID);
          }

          // 3. 记录所有invoke_id并建立映射关系，标记命令已发送
          {
            std::lock_guard<std::mutex> lock(response_requests_mutex_);
            auto it = response_requests_.find(request_key);
            if (it != response_requests_.end()) {
              for (const auto& common_msg : common_list) {
                it->second.sent_invoke_ids.push_back(common_msg.invoke_id);
                // 建立 invoke_id 到 request_key 的映射（优化响应匹配性能）
                invoke_id_to_request_key_[common_msg.invoke_id] = request_key;
              }
              it->second.commands_sent = true;
            }
          }

          // 4. 发送所有召唤命令到Service
          for (const auto& common_msg : common_list) {
            if (observer_) {
              auto result = observer_->SendCommand(common_msg, base::SERVICE_SUBJECT_ID);
              if (result) {
                spdlog::debug("Sent call command to Service: invoke_id={}", common_msg.invoke_id);
              } else {
                spdlog::error("Failed to send call command to Service: invoke_id={}",
                              common_msg.invoke_id);
                return;
              }
            } else {
              spdlog::error("Observer not available");
              return;
            }
          }

          spdlog::debug("All call commands sent successfully");
        }
      }

      void ProtocolGateway::HandleControlCommand(const base::ProtocolFrame& frame) {
        // 控制命令处理（参考原始 CVT_TO_CTRL 处理）
        spdlog::debug("Handling control command from connection {}", frame.frame_id);

        // 添加到响应处理队列（控制命令）
        if (!AddFrameToResponseRequest(frame, true)) {  // true = 控制命令
          spdlog::error("Failed to add frame to response request");
          return;
        }
      }

      void ProtocolGateway::HandleLocalResponse(const base::ProtocolFrame& frame) {
        // 本地直接响应处理（参考原始 CVT_FROM_LOCAL 处理）
        spdlog::debug("Handling local response frame from connection {}", frame.frame_id);

        // 放入本地任务队列，由 LocalTaskThreadLoop 处理
        {
          std::lock_guard<std::mutex> lock(local_task_queue_mutex_);
          local_task_queue_.push(frame);
        }
        local_task_queue_cv_.notify_one();
      }

      void ProtocolGateway::HandleEventFrame(const base::ProtocolFrame& frame) {
        // 事件帧处理（参考原始 CVT_TO_EVENT 处理）
        spdlog::debug("Handling event frame from connection {}", frame.frame_id);

        std::vector<base::EventMessage> event_list;
        int ret = protocol_transform_->ConvertProToEventMsg(frame, event_list);
        if (ret != 0) {
          spdlog::error("Failed to convert frame to event message");
          return;
        }

        // 处理事件消息
        for (const auto& event_msg : event_list) {
          // 添加到事件队列
          {
            std::lock_guard<std::mutex> lock(event_queue_mutex_);
            event_queue_.push(event_msg);
          }
          event_queue_cv_.notify_one();
        }
      }

      bool ProtocolGateway::PackageTimeoutHandle(const std::string& request_key,
                                                 ResponseRequest& request) {
        // 参考原始 __PackegTimeOutHandle 实现

        auto now = std::chrono::system_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - request.create_time);

        // 获取超时时间（参考原始 GetFunTimeOut 逻辑）
        uint32_t timeout_seconds = request_timeout_seconds_;
        if (timeout_seconds <= 0) {
          timeout_seconds = 60;  // 默认超时60秒
        }

        // 检查是否超时
        if (elapsed.count() >= timeout_seconds) {
          if (request.nx_response_list.empty()) {
            // 没有收到任何响应，生成失败响应（参考原始 _make_commonmsg_failed_response）
            base::CommonMessage failed_response;
            // 每个Gateway对应一个连接，使用第一个frame的frame_id作为source_id
            failed_response.source_id
                = request.frame_list.empty() ? 0 : request.frame_list[0].frame_id;
            failed_response.type = base::MessageType::COMMAND;  // 使用已有的类型
            failed_response.b_lastmsg = true;

            // 创建失败响应数据（可以是空数据或错误码）
            // 这里简化处理，实际应该根据协议格式生成失败响应数据

            request.nx_response_list.push_back(failed_response);

            spdlog::warn(
                "PackageTimeoutHandle: Request timeout, generated failed response: key={}, "
                "elapsed={}s",
                request_key, elapsed.count());
          }
        }

        return true;
      }

      void ProtocolGateway::AddServiceResponseToRequest(const base::CommonMessage& result_msg) {
        spdlog::debug("Gateway received response from Service: invoke_id={}, b_lastmsg={}",
                      result_msg.invoke_id, result_msg.b_lastmsg);

        std::lock_guard<std::mutex> lock(response_requests_mutex_);

        // 优化：使用映射表直接查找 invoke_id 对应的 request_key（O(1)复杂度）
        auto invoke_it = invoke_id_to_request_key_.find(result_msg.invoke_id);
        if (invoke_it == invoke_id_to_request_key_.end()) {
          spdlog::warn("No matching request found for response: invoke_id={}",
                       result_msg.invoke_id);
          return;
        }

        const std::string& request_key = invoke_it->second;

        // 查找对应的请求
        auto request_it = response_requests_.find(request_key);
        if (request_it == response_requests_.end()) {
          spdlog::error("Request key found in mapping but not in requests: key={}, invoke_id={}",
                        request_key, result_msg.invoke_id);
          // 清理无效的映射
          invoke_id_to_request_key_.erase(invoke_it);
          return;
        }

        // 添加响应到结果列表
        request_it->second.nx_response_list.push_back(result_msg);

        spdlog::debug(
            "Added response to request: key={}, invoke_id={}, total_responses={}, b_lastmsg={}",
            request_key, result_msg.invoke_id, request_it->second.nx_response_list.size(),
            result_msg.b_lastmsg);
      }

      void ProtocolGateway::OnServiceEvent(const base::EventMessage& event_msg) {
        // 添加到事件队列
        {
          std::lock_guard<std::mutex> lock(event_queue_mutex_);
          event_queue_.push(event_msg);
          event_queue_cv_.notify_one();
        }
      }

      void ProtocolGateway::SetSendCallback(
          std::function<bool(const std::vector<uint8_t>&)> callback) {
        send_callback_ = callback;
      }

      // 线程方法实现
      void ProtocolGateway::ProtocolFrameProcessorLoop() {
        spdlog::info("协议帧处理线程启动 (参考原始 __DoCallAskLoop)");

        while (!should_stop_.load()) {
          base::ProtocolFrame frame;

          // 等待原始协议帧（参考原始 m_ProCmdDeque）
          {
            std::unique_lock<std::mutex> lock(protocol_cmd_queue_mutex_);
            protocol_cmd_queue_cv_.wait(
                lock, [this] { return !protocol_cmd_queue_.empty() || should_stop_.load(); });

            if (should_stop_.load()) break;

            if (!protocol_cmd_queue_.empty()) {
              frame = protocol_cmd_queue_.front();
              protocol_cmd_queue_.pop();
            } else {
              continue;
            }
          }

          // 使用Transform判断协议帧类型（参考原始 GetCvtTypeByProInf）
          if (!protocol_transform_) {
            spdlog::error("Protocol transform not available");
            continue;
          }

          base::ProtocolConvertType convert_type
              = protocol_transform_->GetConvertTypeByFrame(frame);

          switch (convert_type) {
            case base::ProtocolConvertType::TO_CALL:
              // 单帧召唤命令，直接转换并放入Command队列
              HandleSingleFrameCommand(frame);
              break;

            case base::ProtocolConvertType::TO_CTRL:
              // 控制命令（可能单帧或多帧），加入响应处理队列
              HandleControlCommand(frame);
              break;

            case base::ProtocolConvertType::FROM_LOCAL:
              // 本地直接响应，放入本地任务队列
              HandleLocalResponse(frame);
              break;

            case base::ProtocolConvertType::TO_EVENT:
              // 事件帧，直接转换并放入Event队列
              HandleEventFrame(frame);
              break;

            default:
              spdlog::error("Unknown protocol convert type for frame from connection {}",
                            frame.frame_id);
              break;
          }

          std::this_thread::sleep_for(std::chrono::milliseconds(thread_sleep_ms_));
        }

        spdlog::info("协议帧处理线程停止");
      }

      void ProtocolGateway::LocalTaskProcessorLoop() {
        spdlog::info("LocalTaskProcessorLoop Started");

        while (!should_stop_.load()) {
          base::ProtocolFrame frame;

          // 等待本地任务（参考原始线程池）
          {
            std::unique_lock<std::mutex> lock(local_task_queue_mutex_);
            local_task_queue_cv_.wait(
                lock, [this] { return !local_task_queue_.empty() || should_stop_.load(); });

            if (should_stop_.load()) break;

            if (!local_task_queue_.empty()) {
              frame = local_task_queue_.front();
              local_task_queue_.pop();
            } else {
              continue;
            }
          }

          try {
            // 处理本地响应任务（参考原始 __GetInfoFromLocal）
            spdlog::debug("Processing local task for frame from connection {}", frame.frame_id);

            // 使用协议转换器的本地直接处理方法
            if (protocol_transform_) {
              base::ProtocolFrameList response_frames;
              int result = protocol_transform_->DirectResponseFromLocal(frame, response_frames);

              if (result == 0 && !response_frames.empty()) {
                // 发送所有响应帧
                for (const auto& response_frame : response_frames) {
                  if (send_callback_ && !response_frame.data.empty()) {
                    send_callback_(response_frame.data);
                    spdlog::info("Sent local response frame: type={}, data_size={}",
                                 response_frame.type, response_frame.data.size());
                  }
                }
              } else {
                spdlog::error("Failed to generate local response: result={}, response_count={}",
                              result, response_frames.size());

                // 发送错误响应
                if (send_callback_) {
                  std::vector<uint8_t> error_response
                      = {0x68, 0x04, 0x2F, 0x00, 0x00, 0x00};  // 否定确认
                  send_callback_(error_response);
                  spdlog::debug("Sent error response for failed local processing");
                }
              }
            } else {
              spdlog::error("Protocol transform not available for local processing");
            }

          } catch (const std::exception& e) {
            spdlog::error("Exception in local task thread: {}", e.what());
          }
        }

        spdlog::info("LocalTaskProcessorLoop Stoped");
      }

      void ProtocolGateway::ResponseMatcherLoop() {
        spdlog::info("ResponseMatcherLoop Started");

        while (!should_stop_.load()) {
          try {
            CallResponseHandle();
            std::this_thread::sleep_for(std::chrono::milliseconds(thread_sleep_ms_));
          } catch (const std::exception& e) {
            spdlog::error("Exception in response matcher loop: {}", e.what());
          }
        }

        spdlog::info("ResponseMatcherLoop Stopped");
      }

      void ProtocolGateway::CallResponseHandle() {
        std::lock_guard<std::mutex> lock(response_requests_mutex_);
        auto it = response_requests_.begin();

        while (it != response_requests_.end()) {
          auto& request = it->second;

          // 控制命令处理（参考原始 __PackegCtrlCmdHandle 逻辑）
          // 控制命令转换处理：为控制命令、收到完整帧序列且还没有发送给Service
          if (request.is_ctrl_cmd && !request.commands_sent) {
            if (!ProcessControlCommand(it->first, request)) {
              // 处理失败，清理映射并删除该请求（参考原始逻辑）
              CleanupInvokeIdMappings(request);
              it = response_requests_.erase(it);
              spdlog::error("Control command processing failed, removed request");
              continue;
            } else {
              // 成功处理，下个消息包（参考原始第267行逻辑）
              ++it;
            }
            continue;  // 参考原始第269行，控制命令处理后continue
          }

          // 响应结果处理（参考原始 __PackegResultHandle 逻辑）
          // 确认NX命令映射表中是否都收到了结果，收到了就转换为协议并删除该条，否则等待
          if (PackageResultHandle(it->first, request)) {
            // 响应处理完成，清理映射并删除请求
            CleanupInvokeIdMappings(request);
            it = response_requests_.erase(it);
            continue;
          }

          // 超时处理（参考原始 __PackegTimeOutHandle 逻辑）
          // 超时处理（如果超时则直接生成失败响应）
          PackageTimeoutHandle(it->first, request);

          ++it;
        }
      }

      void ProtocolGateway::EventProcessorLoop() {
        spdlog::info("EventProcessorLoop Started");

        while (!should_stop_.load()) {
          base::EventMessage event_message;

          // 等待事件
          {
            std::unique_lock<std::mutex> lock(event_queue_mutex_);
            event_queue_cv_.wait(lock,
                                 [this] { return !event_queue_.empty() || should_stop_.load(); });

            if (should_stop_.load()) break;

            if (!event_queue_.empty()) {
              event_message = event_queue_.front();
              event_queue_.pop();
            } else {
              continue;
            }
          }

          try {
            // 转换事件为协议帧（参考多帧转发逻辑）
            if (!protocol_transform_) {
              spdlog::error("Protocol transform not available for event conversion");
              continue;
            }

            base::ProtocolFrameList result_frames;
            int result = protocol_transform_->ConvertEventMsgToPro(event_message, result_frames);
            if (result != 0) {
              spdlog::error("Failed to convert EventMessage to ProtocolFrame: result={}", result);
              continue;
            }

            // 发送所有生成的协议帧（每个Gateway只发送给自己的连接）
            if (send_callback_) {
              int sent_count = 0;
              for (const auto& frame : result_frames) {
                if (!frame.data.empty()) {
                  // 每个Gateway只发送给自己的连接，不需要考虑frame_id
                  bool send_result = send_callback_(frame.data);
                  if (send_result) {
                    sent_count++;
                    spdlog::debug("Sent event frame: {} bytes, frame_type={}", frame.data.size(),
                                  frame.type);
                  } else {
                    spdlog::error("Failed to send event frame");
                  }
                }
              }

              if (sent_count > 0) {
                spdlog::debug("Successfully sent {} event frames, type: {}", sent_count,
                              event_message.event_type);
              } else {
                spdlog::warn("No event frames sent, type: {}", event_message.event_type);
              }
            }

          } catch (const std::exception& e) {
            spdlog::error("Exception in event processor: {}", e.what());
          }
        }

        spdlog::info("EventProcessorLoop Stoped");
      }

      std::vector<uint8_t> ProtocolGateway::ConvertEventToProtocolBytes(
          const base::EventMessage& event) {
        if (!protocol_transform_) {
          spdlog::error("Protocol transform not available for event conversion");
          return {};
        }

        // 使用Transform将EventMessage转换为ProtocolFrame（参考原始EventOperation）
        base::ProtocolFrameList result_frames;
        int result = protocol_transform_->ConvertEventMsgToPro(event, result_frames);
        if (result != 0) {
          spdlog::error("Failed to convert EventMessage to ProtocolFrame: result={}", result);
          return {};
        }

        // 如果有转换结果，返回第一个帧的数据
        if (!result_frames.empty()) {
          const auto& frame = result_frames.front();
          spdlog::debug(
              "Successfully converted EventMessage to protocol bytes: frame_type={}, data_size={}",
              frame.type, frame.data.size());
          return frame.data;
        }

        spdlog::warn("No protocol frames generated from EventMessage");
        return {};
      }

      // 组件创建方法
      bool ProtocolGateway::CreateProtocolTransform() {
        transform::ProtocolTransformFactory::ProtocolType type;

        if (protocol_type_ == "gw104") {
          type = transform::ProtocolTransformFactory::ProtocolType::gw104;
        } else {
          spdlog::error("Unsupported protocol type: {}", protocol_type_);
          return false;
        }

        protocol_transform_ = transform::ProtocolTransformFactory::CreateTransform(type);
        if (!protocol_transform_) {
          spdlog::error("Failed to create transform for protocol: {}", protocol_type_);
          return false;
        }

        spdlog::info("Created protocol transform: {}", protocol_type_);
        return true;
      }

      bool ProtocolGateway::CreateProtocolService() {
        // 创建Service，使用标准固定ID
        protocol_service_ = std::make_unique<service::ProtocolService>(mediator_);
        if (!protocol_service_) {
          spdlog::error("Failed to create protocol service");
          return false;
        }

        // 将Transform注入到Service中
        // 注意：这里需要创建Transform的副本，因为Gateway也需要使用Transform
        auto service_transform = transform::ProtocolTransformFactory::CreateTransform(
            transform::ProtocolTransformFactory::ProtocolType::gw104);
        protocol_service_->SetProtocolTransform(std::move(service_transform));

        // 初始化并启动Service
        if (!protocol_service_->Initialize()) {
          spdlog::error("Failed to initialize protocol service");
          return false;
        }

        if (!protocol_service_->Start()) {
          spdlog::error("Failed to start protocol service");
          return false;
        }

        spdlog::info("Created and started protocol service");
        return true;
      }

      // 协议帧解析方法
      bool ProtocolGateway::ParseProtocolFrame(const std::vector<uint8_t>& data,
                                               base::ProtocolFrame& frame) {
        if (data.empty()) {
          return false;
        }

        // 只使用 Message 反序列化，失败就丢弃
        base::Message msg;
        size_t parsed = msg.deserialize(data);

        if (parsed > 0) {
          // 成功解析为协议消息
          frame.data = data;
          frame.timestamp = std::chrono::system_clock::now();
          frame.type = msg.getTyp();
          frame.cot = msg.getCot();
          frame.target_addr = msg.getTarget();  // 使用目标地址作为ASDU地址
          frame.vsq = msg.getVsq();

          // 简化：所有帧都视为单帧
          frame.is_last_frame = true;
          frame.frame_id = 0;  // 这里会在 OnNetworkProtocolData 中设置为实际的连接ID

          // 填充基本协议字段
          frame.source_addr = msg.getSource();
          frame.fun = msg.getFun();
          frame.inf = msg.getInf();

          spdlog::debug(
              "Parsed protocol frame: TYP={:02X}, VSQ={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, "
              "FUN={:02X}, INF={:02X}, is_last={}, data_len={}",
              msg.getTyp(), msg.getVsq(), msg.getCot(), msg.getSource(), msg.getTarget(),
              msg.getFun(), msg.getInf(), frame.is_last_frame, data.size());

          return true;
        } else {
          // 解析失败，丢弃数据
          spdlog::warn("Failed to parse protocol frame, discarding data of length {}", data.size());
          return false;
        }
      }

      bool ProtocolGateway::IsLastFrame(const base::ProtocolFrame& frame) {
        return frame.is_last_frame;
      }

      // 响应处理辅助方法实现

      bool ProtocolGateway::AddFrameToResponseRequest(const base::ProtocolFrame& frame,
                                                      bool is_ctrl_cmd) {
        std::lock_guard<std::mutex> lock(response_requests_mutex_);

        std::string request_key = GenerateResponseRequestKey(frame);

        // 简化逻辑：每个请求都是独立的单帧请求
        // 查找是否已存在相关的响应请求
        auto it = response_requests_.find(request_key);
        if (it != response_requests_.end()) {
          // 如果已存在，说明是重复请求，直接返回成功
          spdlog::debug("Request already exists: key={}", request_key);
          return true;
        }

        // 创建新的响应请求（简化版本）
        ResponseRequest new_request;
        new_request.frame_list.push_back(frame);
        new_request.is_ctrl_cmd = is_ctrl_cmd;
        new_request.create_time = std::chrono::system_clock::now();

        response_requests_[request_key] = new_request;

        spdlog::debug("Created new {} request: key={}, vsq={:02X}",
                      is_ctrl_cmd ? "control command" : "call command", request_key, frame.vsq);

        return true;
      }

      std::string ProtocolGateway::GenerateResponseRequestKey(const base::ProtocolFrame& frame) {
        // 生成响应请求的唯一键（每个Gateway只对应一个连接，不需要conn_id）
        return std::to_string(frame.type) + "_" + std::to_string(frame.source_addr) + "_"
               + std::to_string(frame.target_addr) + "_" + std::to_string(frame.cpu) + "_"
               + std::to_string(frame.zone) + "_" + std::to_string(frame.cot) + "_"
               + std::to_string(frame.fun);
      }

      // RequestMatcherThreadLoop 中使用的方法实现

      bool ProtocolGateway::ProcessControlCommand(const std::string& request_key,
                                                  ResponseRequest& request) {
        spdlog::debug("Processing control command: key={}", request_key);

        std::vector<base::CommonMessage> common_list;
        base::ProtocolFrameList result_frames;

        // 转换控制命令
        int ret = protocol_transform_->ConvertProToCommonMsg(request.frame_list, common_list,
                                                             result_frames);
        if (ret != 0) {
          spdlog::error("Failed to convert control command");

          // 发送错误回应
          for (const auto& result_frame : result_frames) {
            if (send_callback_) {
              send_callback_(result_frame.data);
            }
          }
          return false;
        }

        // 处理转换成功的控制命令（支持多条CommonMessage）
        if (common_list.empty()) {
          spdlog::error("No common message generated from control command");
          return false;
        }

        // 发送所有控制命令到Service
        for (auto& common_msg : common_list) {
          // 每个Gateway对应一个连接，使用第一个frame的frame_id作为source_id
          common_msg.source_id = request.frame_list.empty() ? 0 : request.frame_list[0].frame_id;
          common_msg.target_id = base::SERVICE_SUBJECT_ID;
          common_msg.invoke_id = utils::invoke_id::Generate(base::GATEWAY_OBSERVER_ID);

          if (observer_) {
            auto result = observer_->SendCommand(common_msg, base::SERVICE_SUBJECT_ID);
            if (result) {
              spdlog::debug("Sent control command to Service: invoke_id={}", common_msg.invoke_id);
            } else {
              spdlog::error("Failed to send control command to Service: invoke_id={}",
                            common_msg.invoke_id);
              return false;
            }
          } else {
            spdlog::error("Observer not available");
            return false;
          }
        }

        // 记录所有invoke_id并建立映射关系
        for (const auto& common_msg : common_list) {
          request.sent_invoke_ids.push_back(common_msg.invoke_id);
          // 建立 invoke_id 到 request_key 的映射（优化响应匹配性能）
          invoke_id_to_request_key_[common_msg.invoke_id] = request_key;
        }

        // 标记命令已发送（这是关键！）
        request.commands_sent = true;

        spdlog::debug("All control commands sent successfully, count={}, commands_sent=true",
                      common_list.size());
        return true;
      }

      bool ProtocolGateway::PackageResultHandle(const std::string& request_key,
                                                ResponseRequest& request) {
        // 参考原始 __PackegResultHandle 实现

        spdlog::debug(
            "PackageResultHandle: Checking request key={}, response_count={}, commands_sent={}",
            request_key, request.nx_response_list.size(), request.commands_sent);

        // 确认是否收到了所有响应（参考原始逻辑中的 bRecvAllResult 检查）
        if (request.nx_response_list.empty()) {
          spdlog::debug("PackageResultHandle: No responses received yet for key={}", request_key);
          return false;  // 没有收到任何响应
        }

        // 检查最后一个响应的 b_lastmsg 标志（参考原始多帧响应处理）
        const auto& last_response = request.nx_response_list.back();
        spdlog::debug("PackageResultHandle: Last response b_lastmsg={} for key={}",
                      last_response.b_lastmsg, request_key);

        if (!last_response.b_lastmsg) {
          spdlog::debug("PackageResultHandle: Waiting for more responses for key={}", request_key);
          return false;  // 还有后续响应，继续等待
        }

        // 开始转换（参考原始转换逻辑）
        spdlog::debug(
            "PackageResultHandle: Processing complete response for key={}, response_count={}",
            request_key, request.nx_response_list.size());

        // 转换响应为协议帧并发送（参考原始 ConvertCommonMsgToPro 和 __PutProDataToTransObj）
        ProcessCompleteResponse(request_key, request);

        spdlog::debug("PackageResultHandle: Completed processing for key={}", request_key);
        return true;  // 处理完成
      }

      void ProtocolGateway::CleanupInvokeIdMappings(const ResponseRequest& request) {
        // 清理该请求相关的所有 invoke_id 映射
        for (const auto& invoke_id : request.sent_invoke_ids) {
          invoke_id_to_request_key_.erase(invoke_id);
        }
        spdlog::debug("Cleaned up {} invoke_id mappings", request.sent_invoke_ids.size());
      }

      void ProtocolGateway::ProcessCompleteResponse(const std::string& request_key,
                                                    ResponseRequest& request) {
        spdlog::debug("Processing complete response: key={}, responses={}", request_key,
                      request.nx_response_list.size());

        if (!protocol_transform_) {
          spdlog::error("Protocol transform not available");
          return;
        }

        // 将所有响应转换为协议帧并发送（参考原始转换逻辑）
        for (const auto& response : request.nx_response_list) {
          // 使用Transform将CommonMessage转换为ProtocolFrame
          base::ProtocolFrameList cmd_frames;     // 空的命令帧列表
          base::ProtocolFrameList result_frames;  // 输出的结果帧列表

          // 调用Transform的ConvertCommonMsgToPro方法
          int result
              = protocol_transform_->ConvertCommonMsgToPro(response, cmd_frames, result_frames);
          if (result != 0) {
            spdlog::error("Failed to convert CommonMessage to ProtocolFrame: result={}", result);
            continue;
          }

          // 发送转换后的所有协议帧数据
          for (const auto& response_frame : result_frames) {
            if (send_callback_ && !response_frame.data.empty()) {
              send_callback_(response_frame.data);
              spdlog::debug("Sent converted response: {} bytes, frame_type={}, b_lastmsg={}",
                            response_frame.data.size(), response_frame.type, response.b_lastmsg);
            } else {
              spdlog::warn("Cannot send response: callback not set or empty frame data");
            }
          }
        }

        spdlog::info("Completed response processing: key={}, sent {} frames", request_key,
                     request.nx_response_list.size());
      }

    }  // namespace gateway
  }  // namespace protocol
}  // namespace zexuan
